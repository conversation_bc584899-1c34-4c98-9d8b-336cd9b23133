package com.maguo.loan.cash.flow.entrance.ppd.exception;
import com.maguo.loan.cash.flow.entrance.ppd.common.BizException;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.entrance.ppd.dto.ApiResult;
import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdOrderStatus;
import com.maguo.loan.cash.flow.service.WarningService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


@RestControllerAdvice(basePackages = {"com.maguo.loan.cash.flow.entrance.ppd.controller"})
public class PpdGlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(PpdGlobalExceptionHandler.class);

    @Resource
    private WarningService ppdWarningService;

    @ExceptionHandler(value = ValidationException.class)
    public ApiResult bizHandler(ValidationException e) {
        logger.error("拍拍贷-分润 参数校验失败", e);
        return CommonResult.assembleResponse(PpdOrderStatus.FAIL, e.getMessage(), PpdResultCode.INVALID_PARAM);
    }

    @ExceptionHandler(value = PpdBizException.class)
    public ApiResult bizHandler(PpdBizException e) {
        logger.error("拍拍贷-分润 子业务异常", e);
        return CommonResult.assembleResponse(PpdOrderStatus.SYS_ERROR, e.getMessage(), PpdResultCode.SYSTEM_ERROR);
    }

    @ExceptionHandler(value = BizException.class)
    public ApiResult bizHandler(BizException e) {
        logger.error("拍拍贷-分润 业务异常:", e);
        if (e.getResultCode().getWarning()) {
            ppdWarningService.warn("拍拍贷 业务异常:" + e.getMessage());
        }
        return CommonResult.assembleResponse(PpdOrderStatus.SYS_ERROR, e.getMessage(), PpdResultCode.SYSTEM_ERROR);
    }


    @ExceptionHandler(value = Exception.class)
    public ApiResult sysHandler(Exception e) {
        ppdWarningService.warn("拍拍贷-分润 系统异常:" + e.getMessage(), msg -> logger.error("系统异常:", e));
        return CommonResult.assembleResponse(PpdOrderStatus.SYS_ERROR, e.getMessage(), PpdResultCode.SYSTEM_ERROR);
    }

}
